import React, { useState } from 'react';
import { Layout, Menu, Button, Avatar, Dropdown, Space, Badge, theme } from 'antd';
import { Outlet, useNavigate, useLocation } from 'react-router-dom';
import {
  MenuFoldOutlined,
  MenuUnfoldOutlined,
  DashboardOutlined,
  UserOutlined,
  SettingOutlined,
  FileTextOutlined,
  TeamOutlined,
  BellOutlined,
  LogoutOutlined,
  ProfileOutlined,
} from '@ant-design/icons';
import type { MenuProps } from 'antd';

const { Header, Sider, Content } = Layout;

export const DefaultLayout: React.FC = () => {
  const [collapsed, setCollapsed] = useState(false);
  const navigate = useNavigate();
  const location = useLocation();
  const {
    token: { colorBgContainer, borderRadiusLG },
  } = theme.useToken();

  // 获取当前选中的菜单项
  const getSelectedKeys = () => {
    const path = location.pathname;
    if (path === '/' || path === '/dashboard') return ['dashboard'];
    if (path === '/users') return ['users'];
    if (path === '/tasks') return ['tasks'];
    if (path === '/teams') return ['teams'];
    return ['dashboard'];
  };

  // 菜单点击处理
  const handleMenuClick = ({ key }: { key: string }) => {
    switch (key) {
      case 'dashboard':
        navigate('/dashboard');
        break;
      case 'users':
        navigate('/users');
        break;
      case 'tasks':
        navigate('/tasks');
        break;
      case 'teams':
        navigate('/teams');
        break;
      default:
        break;
    }
  };

  // 侧边栏菜单项
  const menuItems: MenuProps['items'] = [
    {
      key: 'dashboard',
      icon: <DashboardOutlined />,
      label: '仪表板',
    },
    {
      key: 'users',
      icon: <UserOutlined />,
      label: '用户管理',
    },
    {
      key: 'tasks',
      icon: <FileTextOutlined />,
      label: '任务管理',
    },
    {
      key: 'teams',
      icon: <TeamOutlined />,
      label: '团队协作',
    },
    {
      key: 'sub1',
      label: '系统设置',
      icon: <SettingOutlined />,
      children: [
        {
          key: 'settings-basic',
          label: '基础设置',
        },
        {
          key: 'settings-permission',
          label: '权限管理',
        },
        {
          key: 'settings-logs',
          label: '系统日志',
        },
      ],
    },
  ];

  // 用户下拉菜单
  const userMenuItems: MenuProps['items'] = [
    {
      key: 'profile',
      icon: <ProfileOutlined />,
      label: '个人资料',
    },
    {
      key: 'settings',
      icon: <SettingOutlined />,
      label: '账户设置',
    },
    {
      type: 'divider',
    },
    {
      key: 'logout',
      icon: <LogoutOutlined />,
      label: '退出登录',
      danger: true,
    },
  ];

  return (
    <Layout style={{ minHeight: '100vh' }}>
      {/* 侧边栏 */}
      <Sider
        trigger={null}
        collapsible
        collapsed={collapsed}
        width={240}
        style={{
          background: colorBgContainer,
          boxShadow: '2px 0 8px 0 rgba(29, 35, 41, 0.05)',
          borderRight: '1px solid #f0f0f0',
        }}
      >
        {/* Logo 区域 */}
        <div
          style={{
            height: 64,
            display: 'flex',
            alignItems: 'center',
            justifyContent: collapsed ? 'center' : 'flex-start',
            padding: collapsed ? '0' : '0 24px',
            borderBottom: '1px solid #f0f0f0',
            marginBottom: 8,
          }}
        >
          <div
            style={{
              fontSize: collapsed ? 20 : 18,
              fontWeight: 'bold',
              color: '#1890ff',
              transition: 'all 0.3s',
            }}
          >
            {collapsed ? 'A' : 'Admin Pro'}
          </div>
        </div>

        {/* 菜单 */}
        <Menu
          theme='light'
          mode='inline'
          selectedKeys={getSelectedKeys()}
          onClick={handleMenuClick}
          items={menuItems}
          style={{
            border: 'none',
            fontSize: 14,
          }}
        />
      </Sider>

      {/* 主要内容区域 */}
      <Layout>
        {/* 顶部导航栏 */}
        <Header
          style={{
            padding: '0 24px',
            background: colorBgContainer,
            borderBottom: '1px solid #f0f0f0',
            display: 'flex',
            alignItems: 'center',
            justifyContent: 'space-between',
            boxShadow: '0 1px 4px rgba(0,21,41,.08)',
          }}
        >
          {/* 左侧：折叠按钮 */}
          <Button
            type='text'
            icon={collapsed ? <MenuUnfoldOutlined /> : <MenuFoldOutlined />}
            onClick={() => setCollapsed(!collapsed)}
            style={{
              fontSize: '16px',
              width: 40,
              height: 40,
            }}
          />

          {/* 右侧：用户信息和通知 */}
          <Space size='middle'>
            {/* 通知铃铛 */}
            <Badge count={5} size='small'>
              <Button
                type='text'
                icon={<BellOutlined />}
                style={{
                  fontSize: '16px',
                  width: 40,
                  height: 40,
                }}
              />
            </Badge>

            {/* 用户头像和下拉菜单 */}
            <Dropdown menu={{ items: userMenuItems }} placement='bottomRight' arrow>
              <Space style={{ cursor: 'pointer', padding: '8px 12px', borderRadius: 8 }}>
                <Avatar size='small' icon={<UserOutlined />} style={{ backgroundColor: '#1890ff' }} />
                <span style={{ fontSize: 14, fontWeight: 500 }}>管理员</span>
              </Space>
            </Dropdown>
          </Space>
        </Header>

        {/* 内容区域 */}
        <Content
          style={{
            margin: '24px',
            padding: '24px',
            minHeight: 280,
            background: colorBgContainer,
            borderRadius: borderRadiusLG,
            boxShadow: '0 1px 2px 0 rgba(0, 0, 0, 0.03), 0 1px 6px -1px rgba(0, 0, 0, 0.02), 0 2px 4px 0 rgba(0, 0, 0, 0.02)',
          }}
        >
          <Outlet />
        </Content>
      </Layout>
    </Layout>
  );
};

// 保持向后兼容
export const ResponsiveLayout = DefaultLayout;
