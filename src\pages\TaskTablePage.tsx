import React from 'react';
import { Typography } from 'antd';
import AntdTable from '../components/AntdTable';

const { Title } = Typography;

/**
 * 任务管理页面
 * 展示完整的任务管理表格功能
 */
const TaskTablePage: React.FC = () => {
  return (
    <div className='h-screen flex flex-col'>
      <div className='bg-white border-b p-4 flex-shrink-0'>
        <div className='max-w-7xl mx-auto'>
          <Title level={2} className='mb-0'>
            任务管理系统
          </Title>
          <p className='text-gray-600 mt-2'>完整的任务管理功能，包含查询、新增、编辑、删除、分页等功能</p>
        </div>
      </div>
      <div className='flex-1 overflow-hidden'>
        <AntdTable />
      </div>
    </div>
  );
};

export default TaskTablePage;
